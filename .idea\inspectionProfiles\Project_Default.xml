<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="Eslint" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="PyPackageRequirementsInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredPackages">
        <value>
          <list size="61">
            <item index="0" class="java.lang.String" itemvalue="pdf2docx" />
            <item index="1" class="java.lang.String" itemvalue="fonttools" />
            <item index="2" class="java.lang.String" itemvalue="PyMuPDF" />
            <item index="3" class="java.lang.String" itemvalue="termcolor" />
            <item index="4" class="java.lang.String" itemvalue="python-docx" />
            <item index="5" class="java.lang.String" itemvalue="opencv-python" />
            <item index="6" class="java.lang.String" itemvalue="fire" />
            <item index="7" class="java.lang.String" itemvalue="lxml" />
            <item index="8" class="java.lang.String" itemvalue="numpy" />
            <item index="9" class="java.lang.String" itemvalue="protobuf" />
            <item index="10" class="java.lang.String" itemvalue="wheel" />
            <item index="11" class="java.lang.String" itemvalue="certifi" />
            <item index="12" class="java.lang.String" itemvalue="charset-normalizer" />
            <item index="13" class="java.lang.String" itemvalue="setuptools" />
            <item index="14" class="java.lang.String" itemvalue="requests" />
            <item index="15" class="java.lang.String" itemvalue="urllib3" />
            <item index="16" class="java.lang.String" itemvalue="idna" />
            <item index="17" class="java.lang.String" itemvalue="httpx" />
            <item index="18" class="java.lang.String" itemvalue="PyQt6" />
            <item index="19" class="java.lang.String" itemvalue="loguru" />
            <item index="20" class="java.lang.String" itemvalue="pandas" />
            <item index="21" class="java.lang.String" itemvalue="dwebsocket" />
            <item index="22" class="java.lang.String" itemvalue="django" />
            <item index="23" class="java.lang.String" itemvalue="pymysql" />
            <item index="24" class="java.lang.String" itemvalue="django-threadlocals" />
            <item index="25" class="java.lang.String" itemvalue="pycryptodome" />
            <item index="26" class="java.lang.String" itemvalue="click" />
            <item index="27" class="java.lang.String" itemvalue="django-cors-headers" />
            <item index="28" class="java.lang.String" itemvalue="xlrd" />
            <item index="29" class="java.lang.String" itemvalue="Flask" />
            <item index="30" class="java.lang.String" itemvalue="beautifulsoup4" />
            <item index="31" class="java.lang.String" itemvalue="selenium-wire" />
            <item index="32" class="java.lang.String" itemvalue="pdfplumber" />
            <item index="33" class="java.lang.String" itemvalue="selenium" />
            <item index="34" class="java.lang.String" itemvalue="undetected-chromedriver" />
            <item index="35" class="java.lang.String" itemvalue="pygame" />
            <item index="36" class="java.lang.String" itemvalue="flask-mysqldb" />
            <item index="37" class="java.lang.String" itemvalue="WTForms" />
            <item index="38" class="java.lang.String" itemvalue="Werkzeug" />
            <item index="39" class="java.lang.String" itemvalue="flask-bootstrap-components" />
            <item index="40" class="java.lang.String" itemvalue="flask-wtf" />
            <item index="41" class="java.lang.String" itemvalue="python-dotenv" />
            <item index="42" class="java.lang.String" itemvalue="flask" />
            <item index="43" class="java.lang.String" itemvalue="Bootstrap-Flask" />
            <item index="44" class="java.lang.String" itemvalue="bcrypt" />
            <item index="45" class="java.lang.String" itemvalue="SQLAlchemy" />
            <item index="46" class="java.lang.String" itemvalue="Flask-Bootstrap-Components" />
            <item index="47" class="java.lang.String" itemvalue="PyMySQL" />
            <item index="48" class="java.lang.String" itemvalue="Jinja2" />
            <item index="49" class="java.lang.String" itemvalue="Pillow" />
            <item index="50" class="java.lang.String" itemvalue="openpyxl" />
            <item index="51" class="java.lang.String" itemvalue="fake-useragent" />
            <item index="52" class="java.lang.String" itemvalue="seaborn" />
            <item index="53" class="java.lang.String" itemvalue="matplotlib" />
            <item index="54" class="java.lang.String" itemvalue="pyecharts" />
            <item index="55" class="java.lang.String" itemvalue="Appium-Python-Client" />
            <item index="56" class="java.lang.String" itemvalue="djangorestframework-simplejwt" />
            <item index="57" class="java.lang.String" itemvalue="Django" />
            <item index="58" class="java.lang.String" itemvalue="djangorestframework" />
            <item index="59" class="java.lang.String" itemvalue="plotly" />
            <item index="60" class="java.lang.String" itemvalue="concurrent-futures" />
          </list>
        </value>
      </option>
    </inspection_tool>
    <inspection_tool class="PyUnresolvedReferencesInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredIdentifiers">
        <list>
          <option value="requests.exceptions.*" />
          <option value="pipelinesPractice.pipelinesPractice.spiders.test.draw" />
        </list>
      </option>
    </inspection_tool>
  </profile>
</component>