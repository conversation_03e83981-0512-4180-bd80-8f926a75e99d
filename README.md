# 愤怒的小鸟 - 躲避游戏

一个基于 Pygame 开发的愤怒的小鸟主题躲避游戏。

## 游戏特色

- 🐦 **小鸟控制**: 使用键盘控制小鸟上下左右移动
- 🐷 **障碍物系统**: 随机生成旋转的小猪障碍物
- 💥 **碰撞检测**: 碰到障碍物触发游戏结束
- 🏆 **计分系统**: 躲避障碍物获得分数，记录最高分
- ✨ **视觉效果**: 
  - 小鸟根据移动方向旋转
  - 小鸟移动尾迹效果
  - 小猪旋转动画
  - 碰撞和得分粒子效果
- 📈 **难度递增**: 游戏时间越长，障碍物速度越快，生成频率越高
- 🔄 **重玩功能**: 游戏结束后可以重新开始

## 操作说明

### 移动控制
- **W** 或 **↑**: 向上移动
- **S** 或 **↓**: 向下移动  
- **A** 或 **←**: 向左移动
- **D** 或 **→**: 向右移动

### 游戏控制
- **鼠标点击重玩按钮**: 重新开始游戏
- **R键**: 游戏结束时重新开始

## 游戏规则

1. 控制小鸟躲避从右侧飞来的小猪障碍物
2. 每成功躲避一个小猪障碍物得1分
3. 碰到任何小猪障碍物游戏结束
4. 游戏会自动保存最高分记录
5. 随着游戏进行，难度会逐渐增加

## 安装和运行

### 环境要求
- Python 3.6+
- Pygame 库

### 安装依赖
```bash
pip install pygame
```

### 运行游戏
```bash
python angry_birds_game.py
```

## 文件结构

```
pyGame/
├── angry_birds_game.py    # 主游戏文件
├── README.md             # 说明文档
├── high_score.txt        # 最高分记录文件（自动生成）
└── img/                  # 图片资源文件夹
    ├── 愤怒的小鸟里的小鸟.png
    ├── 愤怒的小鸟里的小猪.png
    ├── 愤怒的小鸟背景图2.jpg
    ├── 愤怒的小鸟gameover图.jpg
    └── 重玩按钮.jpg
```

## 游戏截图说明

游戏界面包含：
- 美观的背景图片
- 实时分数显示
- 最高分记录
- 当前难度等级
- 操作说明提示
- 游戏结束界面和重玩按钮

## 技术特点

- 面向对象设计，代码结构清晰
- 精灵组管理游戏对象
- 粒子系统实现视觉效果
- 碰撞检测算法
- 文件I/O保存游戏数据
- 动态难度调整系统

## 开发者

基于 Pygame 库开发，适合学习游戏开发的初学者参考。

---

享受游戏吧！🎮
