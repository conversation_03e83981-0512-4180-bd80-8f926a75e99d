# 愤怒的小鸟 - 躲避游戏

一个基于 Pygame 开发的愤怒的小鸟主题躲避游戏。

## 游戏特色

- 🐦 **小鸟控制**: 使用键盘控制小鸟上下左右移动
- 🐷 **障碍物系统**: 随机生成旋转的小猪障碍物
- 💥 **碰撞检测**: 碰到障碍物触发游戏结束
- 🏆 **计分系统**: 躲避障碍物获得分数，记录最高分
- ✨ **视觉效果**:
  - 小鸟根据移动方向旋转
  - 小鸟移动尾迹效果
  - 小猪旋转动画
  - 碰撞和得分粒子效果
  - 背景滚动效果增强道路感
  - 云朵动画和地面标记线
  - 实时速度和方向指示器
- 📈 **难度递增**: 游戏时间越长，障碍物速度越快，生成频率越高
- 🔄 **重玩功能**: 游戏结束后可以重新开始

## 操作说明

### 移动控制
- **W** 或 **↑**: 向上移动
- **S** 或 **↓**: 向下移动  
- **A** 或 **←**: 向左移动
- **D** 或 **→**: 向右移动

### 游戏控制
- **鼠标点击重玩按钮**: 重新开始游戏
- **R键**: 游戏结束时重新开始

## 游戏规则

1. 控制小鸟躲避从右侧飞来的小猪障碍物
2. 每成功躲避一个小猪障碍物得1分
3. 碰到任何小猪障碍物游戏结束
4. 游戏会自动保存最高分记录
5. 随着游戏进行，难度会逐渐增加
6. 小鸟可以部分移出屏幕边界，增强移动自由度
7. 背景会持续滚动，营造前进的感觉

## 安装和运行

### 环境要求
- Python 3.6+
- Pygame 库

### 安装依赖
```bash
pip install pygame
```

### 运行游戏
```bash
python angry_birds_game.py
```

## 文件结构

```
pyGame/
├── angry_birds_game.py    # 主游戏文件
├── README.md             # 说明文档
├── high_score.txt        # 最高分记录文件（自动生成）
└── img/                  # 图片资源文件夹
    ├── 愤怒的小鸟里的小鸟.png
    ├── 愤怒的小鸟里的小猪.png
    ├── 愤怒的小鸟背景图2.jpg
    ├── 愤怒的小鸟gameover图.jpg
    └── 重玩按钮.jpg
```

## 游戏截图说明

游戏界面包含：
- 美观的滚动背景图片
- 实时分数显示
- 最高分记录
- 当前难度等级
- 速度和方向指示器
- 操作说明提示
- 游戏结束界面和重玩按钮
- 动态云朵和地面标记效果

## 技术特点

- 面向对象设计，代码结构清晰
- 精灵组管理游戏对象
- 粒子系统实现视觉效果
- 碰撞检测算法
- 文件I/O保存游戏数据
- 动态难度调整系统

## 更新日志

### v1.1 (最新版本)
- ✅ **修复中文显示问题**: 使用系统中文字体，确保中文界面正常显示
- ✅ **修复边界移动限制**: 允许小鸟部分移出屏幕边界，增强移动自由度
- ✅ **增加背景滚动效果**: 持续滚动的背景营造前进感和道路感
- ✅ **添加云朵动画**: 动态云朵增强视觉层次
- ✅ **添加地面标记**: 地面标记线增强速度感
- ✅ **添加速度指示器**: 实时显示小鸟移动速度和方向
- ✅ **优化用户界面**: 更丰富的视觉反馈和交互体验

### v1.0 (初始版本)
- 基础游戏功能实现
- 小鸟控制和障碍物系统
- 碰撞检测和计分系统
- 游戏结束和重玩功能

## 开发者

基于 Pygame 库开发，适合学习游戏开发的初学者参考。

---

享受游戏吧！🎮
