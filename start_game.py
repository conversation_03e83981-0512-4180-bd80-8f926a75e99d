#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
愤怒的小鸟游戏启动器
检查依赖并启动游戏
"""

import sys
import os

def check_pygame():
    """检查pygame是否已安装"""
    try:
        import pygame
        print("✓ Pygame 已安装")
        return True
    except ImportError:
        print("✗ Pygame 未安装")
        print("请运行以下命令安装 Pygame:")
        print("pip install pygame")
        return False

def check_images():
    """检查图片文件是否存在"""
    required_images = [
        "img/愤怒的小鸟里的小鸟.png",
        "img/愤怒的小鸟里的小猪.png", 
        "img/愤怒的小鸟背景图2.jpg",
        "img/愤怒的小鸟gameover图.jpg",
        "img/重玩按钮.jpg"
    ]
    
    missing_files = []
    for image_path in required_images:
        if not os.path.exists(image_path):
            missing_files.append(image_path)
    
    if missing_files:
        print("✗ 缺少以下图片文件:")
        for file in missing_files:
            print(f"  - {file}")
        return False
    else:
        print("✓ 所有图片文件存在")
        return True

def main():
    """主函数"""
    print("=" * 50)
    print("愤怒的小鸟游戏启动器")
    print("=" * 50)
    
    # 检查依赖
    print("\n检查游戏依赖...")
    pygame_ok = check_pygame()
    images_ok = check_images()
    
    if not pygame_ok or not images_ok:
        print("\n❌ 游戏无法启动，请解决上述问题后重试")
        input("按回车键退出...")
        return
    
    print("\n✓ 所有依赖检查通过")
    print("\n🎮 启动游戏...")
    print("\n游戏操作说明:")
    print("- 使用 WASD 或方向键控制小鸟")
    print("- 躲避小猪障碍物获得分数")
    print("- 碰到障碍物游戏结束")
    print("- 点击重玩按钮或按R键重新开始")
    print("\n" + "=" * 50)
    
    # 启动游戏
    try:
        import angry_birds_game
        angry_birds_game.Game().run()
    except Exception as e:
        print(f"\n❌ 游戏启动失败: {e}")
        input("按回车键退出...")

if __name__ == "__main__":
    main()
