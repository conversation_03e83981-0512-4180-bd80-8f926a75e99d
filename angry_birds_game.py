import pygame
import random
import sys
import os
import math

# 初始化pygame
pygame.init()
pygame.mixer.init()

# 游戏常量
SCREEN_WIDTH = 1200
SCREEN_HEIGHT = 800
FPS = 60

# 颜色定义
WHITE = (255, 255, 255)
BLACK = (0, 0, 0)
RED = (255, 0, 0)
GREEN = (0, 255, 0)
BLUE = (0, 0, 255)
YELLOW = (255, 255, 0)
ORANGE = (255, 165, 0)

# 粒子效果类
class Particle:
    def __init__(self, x, y, color):
        self.x = x
        self.y = y
        self.vx = random.uniform(-3, 3)
        self.vy = random.uniform(-3, 3)
        self.color = color
        self.life = 30
        self.max_life = 30

    def update(self):
        self.x += self.vx
        self.y += self.vy
        self.vy += 0.1  # 重力
        self.life -= 1

    def draw(self, screen):
        if self.life > 0:
            alpha = int(255 * (self.life / self.max_life))
            size = int(5 * (self.life / self.max_life))
            if size > 0:
                pygame.draw.circle(screen, (*self.color, alpha), (int(self.x), int(self.y)), size)

class Bird(pygame.sprite.Sprite):
    """小鸟类"""
    def __init__(self):
        super().__init__()
        # 加载小鸟图片
        self.original_image = pygame.image.load("img/愤怒的小鸟里的小鸟.png")
        self.original_image = pygame.transform.scale(self.original_image, (60, 60))
        self.image = self.original_image.copy()
        self.rect = self.image.get_rect()
        self.rect.x = 100
        self.rect.y = SCREEN_HEIGHT // 2
        self.speed = 5
        self.angle = 0
        self.trail = []  # 尾迹效果
        self.velocity_x = 0
        self.velocity_y = 0

    def update(self):
        """更新小鸟位置"""
        keys = pygame.key.get_pressed()

        # 记录移动方向用于旋转
        dx, dy = 0, 0

        # 键盘控制
        if keys[pygame.K_UP] or keys[pygame.K_w]:
            self.rect.y -= self.speed
            dy = -1
            self.velocity_y = -self.speed
        if keys[pygame.K_DOWN] or keys[pygame.K_s]:
            self.rect.y += self.speed
            dy = 1
            self.velocity_y = self.speed
        if keys[pygame.K_LEFT] or keys[pygame.K_a]:
            self.rect.x -= self.speed
            dx = -1
            self.velocity_x = -self.speed
        if keys[pygame.K_RIGHT] or keys[pygame.K_d]:
            self.rect.x += self.speed
            dx = 1
            self.velocity_x = self.speed

        # 如果没有按键，速度逐渐减少
        if not any([keys[pygame.K_UP], keys[pygame.K_DOWN], keys[pygame.K_w], keys[pygame.K_s]]):
            self.velocity_y = 0
        if not any([keys[pygame.K_LEFT], keys[pygame.K_RIGHT], keys[pygame.K_a], keys[pygame.K_d]]):
            self.velocity_x = 0

        # 根据移动方向旋转小鸟
        if dx != 0 or dy != 0:
            self.angle = math.degrees(math.atan2(-dy, dx))
            self.image = pygame.transform.rotate(self.original_image, self.angle)
            old_center = self.rect.center
            self.rect = self.image.get_rect()
            self.rect.center = old_center

        # 添加尾迹效果
        self.trail.append((self.rect.centerx, self.rect.centery))
        if len(self.trail) > 10:
            self.trail.pop(0)

        # 更宽松的边界检测，允许部分移出屏幕
        margin = 30  # 允许移出屏幕的边距
        if self.rect.top < -margin:
            self.rect.top = -margin
        if self.rect.bottom > SCREEN_HEIGHT + margin:
            self.rect.bottom = SCREEN_HEIGHT + margin
        if self.rect.left < -margin:
            self.rect.left = -margin
        if self.rect.right > SCREEN_WIDTH + margin:
            self.rect.right = SCREEN_WIDTH + margin

    def draw_trail(self, screen):
        """绘制尾迹效果"""
        for i, pos in enumerate(self.trail):
            alpha = int(255 * (i / len(self.trail)))
            size = int(3 * (i / len(self.trail)))
            if size > 0:
                color = (255, 255, 0, alpha)  # 黄色尾迹
                pygame.draw.circle(screen, color[:3], pos, size)

class Pig(pygame.sprite.Sprite):
    """小猪障碍物类"""
    def __init__(self):
        super().__init__()
        # 加载小猪图片
        self.original_image = pygame.image.load("img/愤怒的小鸟里的小猪.png")
        self.original_image = pygame.transform.scale(self.original_image, (80, 80))
        self.image = self.original_image.copy()
        self.rect = self.image.get_rect()
        self.rect.x = SCREEN_WIDTH
        self.rect.y = random.randint(0, SCREEN_HEIGHT - 80)
        self.speed = random.randint(3, 7)
        self.scored = False  # 是否已经得分
        self.rotation = 0
        self.rotation_speed = random.uniform(-2, 2)

    def update(self):
        """更新小猪位置"""
        self.rect.x -= self.speed

        # 旋转效果
        self.rotation += self.rotation_speed
        self.image = pygame.transform.rotate(self.original_image, self.rotation)
        old_center = self.rect.center
        self.rect = self.image.get_rect()
        self.rect.center = old_center

        # 如果小猪移出屏幕左侧，删除它
        if self.rect.right < 0:
            self.kill()

class Game:
    """游戏主类"""
    def __init__(self):
        self.screen = pygame.display.set_mode((SCREEN_WIDTH, SCREEN_HEIGHT))
        pygame.display.set_caption("愤怒的小鸟 - 躲避游戏")
        self.clock = pygame.time.Clock()
        
        # 加载背景图片
        self.background = pygame.image.load("img/愤怒的小鸟背景图2.jpg")
        self.background = pygame.transform.scale(self.background, (SCREEN_WIDTH, SCREEN_HEIGHT))

        # 背景滚动效果
        self.bg_x1 = 0
        self.bg_x2 = SCREEN_WIDTH
        self.bg_scroll_speed = 2
        
        # 加载游戏结束图片
        self.gameover_image = pygame.image.load("img/愤怒的小鸟gameover图.jpg")
        self.gameover_image = pygame.transform.scale(self.gameover_image, (400, 300))
        
        # 加载重玩按钮图片
        self.restart_button = pygame.image.load("img/重玩按钮.jpg")
        self.restart_button = pygame.transform.scale(self.restart_button, (150, 60))
        
        # 创建精灵组
        self.all_sprites = pygame.sprite.Group()
        self.pigs = pygame.sprite.Group()

        # 创建小鸟
        self.bird = Bird()
        self.all_sprites.add(self.bird)

        # 游戏状态
        self.game_over = False
        self.score = 0
        self.high_score = self.load_high_score()

        # 设置中文字体
        try:
            # 尝试使用系统中文字体
            self.font = pygame.font.Font("C:/Windows/Fonts/simhei.ttf", 36)
            self.big_font = pygame.font.Font("C:/Windows/Fonts/simhei.ttf", 72)
            self.small_font = pygame.font.Font("C:/Windows/Fonts/simhei.ttf", 24)
        except:
            try:
                # 备选字体
                self.font = pygame.font.Font("C:/Windows/Fonts/msyh.ttc", 36)
                self.big_font = pygame.font.Font("C:/Windows/Fonts/msyh.ttc", 72)
                self.small_font = pygame.font.Font("C:/Windows/Fonts/msyh.ttc", 24)
            except:
                # 如果都找不到，使用系统默认字体
                self.font = pygame.font.SysFont("SimHei,Microsoft YaHei,Arial", 36)
                self.big_font = pygame.font.SysFont("SimHei,Microsoft YaHei,Arial", 72)
                self.small_font = pygame.font.SysFont("SimHei,Microsoft YaHei,Arial", 24)

        # 小猪生成计时器
        self.pig_spawn_timer = 0
        self.pig_spawn_delay = 120  # 2秒生成一个小猪（60FPS）

        # 粒子效果
        self.particles = []

        # 游戏难度
        self.difficulty_timer = 0
        self.base_pig_speed = 3

        # 云朵效果
        self.clouds = []
        self.create_clouds()
        
    def load_high_score(self):
        """加载最高分"""
        try:
            with open("high_score.txt", "r") as f:
                return int(f.read())
        except:
            return 0

    def save_high_score(self):
        """保存最高分"""
        try:
            with open("high_score.txt", "w") as f:
                f.write(str(self.high_score))
        except:
            pass

    def reset_game(self):
        """重置游戏"""
        # 保存最高分
        if self.score > self.high_score:
            self.high_score = self.score
            self.save_high_score()

        # 清除所有小猪
        for pig in self.pigs:
            pig.kill()

        # 重置小鸟位置
        self.bird.rect.x = 100
        self.bird.rect.y = SCREEN_HEIGHT // 2
        self.bird.angle = 0
        self.bird.trail = []

        # 重置游戏状态
        self.game_over = False
        self.score = 0
        self.pig_spawn_timer = 0
        self.difficulty_timer = 0
        self.particles = []

        # 重置背景滚动
        self.bg_x1 = 0
        self.bg_x2 = SCREEN_WIDTH
        
    def spawn_pig(self):
        """生成小猪障碍物"""
        pig = Pig()
        self.all_sprites.add(pig)
        self.pigs.add(pig)
        
    def check_collisions(self):
        """检查碰撞"""
        # 检查小鸟与小猪的碰撞
        hits = pygame.sprite.spritecollide(self.bird, self.pigs, False)
        if hits:
            # 创建爆炸粒子效果
            for _ in range(20):
                particle = Particle(self.bird.rect.centerx, self.bird.rect.centery, RED)
                self.particles.append(particle)
            self.game_over = True
            
    def update_score(self):
        """更新分数"""
        for pig in self.pigs:
            # 如果小猪已经被小鸟超越且还没有得分
            if pig.rect.right < self.bird.rect.left and not pig.scored:
                pig.scored = True
                self.score += 1
                # 创建得分粒子效果
                for _ in range(10):
                    particle = Particle(pig.rect.centerx, pig.rect.centery, GREEN)
                    self.particles.append(particle)

    def update_difficulty(self):
        """更新游戏难度"""
        self.difficulty_timer += 1
        # 每30秒增加难度
        if self.difficulty_timer % (30 * FPS) == 0:
            self.base_pig_speed += 0.5
            self.pig_spawn_delay = max(60, self.pig_spawn_delay - 10)

    def update_background(self):
        """更新背景滚动"""
        self.bg_x1 -= self.bg_scroll_speed
        self.bg_x2 -= self.bg_scroll_speed

        # 当背景图片完全移出屏幕时，重置位置
        if self.bg_x1 <= -SCREEN_WIDTH:
            self.bg_x1 = SCREEN_WIDTH
        if self.bg_x2 <= -SCREEN_WIDTH:
            self.bg_x2 = SCREEN_WIDTH

    def create_clouds(self):
        """创建云朵"""
        self.clouds = []
        for _ in range(5):
            cloud = {
                'x': random.randint(0, SCREEN_WIDTH),
                'y': random.randint(50, 200),
                'size': random.randint(30, 60),
                'speed': random.uniform(0.5, 1.5)
            }
            self.clouds.append(cloud)

    def update_clouds(self):
        """更新云朵位置"""
        for cloud in self.clouds:
            cloud['x'] -= cloud['speed']
            if cloud['x'] < -cloud['size']:
                cloud['x'] = SCREEN_WIDTH + cloud['size']
                cloud['y'] = random.randint(50, 200)

    def draw_clouds(self):
        """绘制云朵"""
        for cloud in self.clouds:
            # 绘制简单的云朵形状
            pygame.draw.circle(self.screen, (255, 255, 255, 180),
                             (int(cloud['x']), int(cloud['y'])), cloud['size'] // 2)
            pygame.draw.circle(self.screen, (255, 255, 255, 180),
                             (int(cloud['x'] - cloud['size'] // 3), int(cloud['y'])), cloud['size'] // 3)
            pygame.draw.circle(self.screen, (255, 255, 255, 180),
                             (int(cloud['x'] + cloud['size'] // 3), int(cloud['y'])), cloud['size'] // 3)

    def draw_background(self):
        """绘制滚动背景"""
        self.screen.blit(self.background, (self.bg_x1, 0))
        self.screen.blit(self.background, (self.bg_x2, 0))

        # 绘制云朵
        self.draw_clouds()

        # 绘制地面标记线（增强道路感）
        for i in range(0, SCREEN_WIDTH + 100, 100):
            x = (i - (self.bg_x1 % 100)) % (SCREEN_WIDTH + 100)
            pygame.draw.line(self.screen, (100, 100, 100),
                           (x, SCREEN_HEIGHT - 50), (x + 50, SCREEN_HEIGHT - 50), 3)
                
    def draw_ui(self):
        """绘制用户界面"""
        # 绘制半透明背景
        ui_surface = pygame.Surface((400, 120))
        ui_surface.set_alpha(128)
        ui_surface.fill(BLACK)
        self.screen.blit(ui_surface, (10, 10))

        # 绘制分数
        score_text = self.font.render(f"分数: {self.score}", True, YELLOW)
        self.screen.blit(score_text, (20, 20))

        # 绘制最高分
        high_score_text = self.font.render(f"最高分: {self.high_score}", True, ORANGE)
        self.screen.blit(high_score_text, (20, 50))

        # 绘制控制说明
        control_text = self.small_font.render("使用 WASD 或方向键控制小鸟", True, WHITE)
        self.screen.blit(control_text, (20, 80))

        # 绘制难度指示
        difficulty_level = self.difficulty_timer // (30 * FPS) + 1
        difficulty_text = self.small_font.render(f"难度等级: {difficulty_level}", True, WHITE)
        self.screen.blit(difficulty_text, (20, 100))

        # 绘制速度指示器
        speed = math.sqrt(self.bird.velocity_x**2 + self.bird.velocity_y**2)
        speed_text = self.small_font.render(f"速度: {speed:.1f}", True, WHITE)
        self.screen.blit(speed_text, (200, 100))

        # 绘制方向指示器（小箭头）
        if speed > 0:
            arrow_x = 350
            arrow_y = 110
            arrow_length = 20
            angle_rad = math.atan2(self.bird.velocity_y, self.bird.velocity_x)
            end_x = arrow_x + arrow_length * math.cos(angle_rad)
            end_y = arrow_y + arrow_length * math.sin(angle_rad)
            pygame.draw.line(self.screen, YELLOW, (arrow_x, arrow_y), (end_x, end_y), 3)
            # 箭头头部
            head_angle1 = angle_rad + 2.5
            head_angle2 = angle_rad - 2.5
            head_length = 8
            head1_x = end_x - head_length * math.cos(head_angle1)
            head1_y = end_y - head_length * math.sin(head_angle1)
            head2_x = end_x - head_length * math.cos(head_angle2)
            head2_y = end_y - head_length * math.sin(head_angle2)
            pygame.draw.line(self.screen, YELLOW, (end_x, end_y), (head1_x, head1_y), 2)
            pygame.draw.line(self.screen, YELLOW, (end_x, end_y), (head2_x, head2_y), 2)
        
    def draw_game_over(self):
        """绘制游戏结束界面"""
        # 半透明遮罩
        overlay = pygame.Surface((SCREEN_WIDTH, SCREEN_HEIGHT))
        overlay.set_alpha(128)
        overlay.fill(BLACK)
        self.screen.blit(overlay, (0, 0))
        
        # 游戏结束图片
        gameover_rect = self.gameover_image.get_rect()
        gameover_rect.center = (SCREEN_WIDTH // 2, SCREEN_HEIGHT // 2 - 100)
        self.screen.blit(self.gameover_image, gameover_rect)
        
        # 最终分数
        final_score_text = self.big_font.render(f"最终分数: {self.score}", True, WHITE)
        final_score_rect = final_score_text.get_rect()
        final_score_rect.center = (SCREEN_WIDTH // 2, SCREEN_HEIGHT // 2 + 50)
        self.screen.blit(final_score_text, final_score_rect)
        
        # 重玩按钮
        restart_rect = self.restart_button.get_rect()
        restart_rect.center = (SCREEN_WIDTH // 2, SCREEN_HEIGHT // 2 + 150)
        self.screen.blit(self.restart_button, restart_rect)
        
        return restart_rect
        
    def run(self):
        """主游戏循环"""
        running = True
        
        while running:
            for event in pygame.event.get():
                if event.type == pygame.QUIT:
                    running = False
                    
                if event.type == pygame.MOUSEBUTTONDOWN and self.game_over:
                    # 检查是否点击了重玩按钮
                    restart_rect = pygame.Rect(SCREEN_WIDTH // 2 - 75, SCREEN_HEIGHT // 2 + 120, 150, 60)
                    if restart_rect.collidepoint(event.pos):
                        self.reset_game()
                        
                if event.type == pygame.KEYDOWN:
                    if event.key == pygame.K_r and self.game_over:
                        self.reset_game()
            
            if not self.game_over:
                # 生成小猪
                self.pig_spawn_timer += 1
                if self.pig_spawn_timer >= self.pig_spawn_delay:
                    self.spawn_pig()
                    self.pig_spawn_timer = 0
                    # 随机调整生成间隔
                    self.pig_spawn_delay = random.randint(60, 180)

                # 更新游戏对象
                self.all_sprites.update()

                # 更新背景滚动
                self.update_background()

                # 更新云朵
                self.update_clouds()

                # 更新难度
                self.update_difficulty()

                # 检查碰撞
                self.check_collisions()

                # 更新分数
                self.update_score()

            # 更新粒子效果
            for particle in self.particles[:]:
                particle.update()
                if particle.life <= 0:
                    self.particles.remove(particle)

            # 绘制游戏
            self.draw_background()

            # 绘制小鸟尾迹
            self.bird.draw_trail(self.screen)

            # 绘制精灵
            self.all_sprites.draw(self.screen)

            # 绘制粒子效果
            for particle in self.particles:
                particle.draw(self.screen)

            self.draw_ui()

            if self.game_over:
                restart_rect = self.draw_game_over()

            pygame.display.flip()
            self.clock.tick(FPS)
        
        pygame.quit()
        sys.exit()

if __name__ == "__main__":
    game = Game()
    game.run()
